# Water Usage Predictor - Input Requirements

## 🎯 **Actual Input Requirements**

Based on the trained XGBoost model, here are the **exact inputs** required for prediction:

### **1. API Input Format**

```python
# Minimum required inputs for prediction
prediction_request = {
    "account_id": 12345,                    # Account to predict for
    "target_year": 2025,                    # Year to predict
    "target_month": 1,                      # Month to predict (1-12)
    
    # Historical usage data (last 6 months minimum)
    "recent_usage": [
        28.5,  # 6 months ago
        30.2,  # 5 months ago  
        29.8,  # 4 months ago
        31.5,  # 3 months ago (usage_lag_3)
        30.0,  # 2 months ago (usage_lag_2)
        32.1   # 1 month ago (usage_lag_1) - most recent
    ],
    
    # Weather data for target month
    "weather": {
        "temperature_min": 23.5,            # °C
        "temperature_max": 31.2,            # °C  
        "rainfall": 8.5,                    # mm
        "humidity": 85.0,                   # %
        "wind_speed": 12.3                  # km/h (optional)
    }
}
```

### **2. Model Feature Computation**

The predictor automatically computes these **22 features** from your inputs:

#### **A. Basic Features (3)**
- `account_id`: Account identifier
- `month`: Target month (1-12)
- `months_since_start`: Account history length

#### **B. Time Series Features (5)**
- `usage_lag_1`: Last month usage
- `usage_lag_2`: 2 months ago usage  
- `usage_lag_3`: 3 months ago usage
- `usage_rolling_mean_3`: Average of last 3 months
- `usage_rolling_mean_6`: Average of last 6 months

#### **C. Weather Features (5)**
- `temperature_min`: Minimum temperature
- `temperature_max`: Maximum temperature
- `rainfall`: Rainfall amount
- `humidity`: Humidity percentage
- `wind_speed`: Wind speed (uses average if not provided)

#### **D. Derived Weather Features (4)**
- `temp_avg`: (temp_max + temp_min) / 2
- `temp_range`: temp_max - temp_min
- `heat_index`: temp_max × (1 + humidity/100)
- `comfort_index`: temp_avg - humidity/10

#### **E. Weather Categories (2)**
- `is_dry_month`: 1 if rainfall < 10mm, else 0
- `is_wet_month`: 1 if rainfall > 20mm, else 0

#### **F. Seasonal Features (2)**
- `month_sin`: sin(2π × month / 12)
- `month_cos`: cos(2π × month / 12)

#### **G. Account-Specific Features (2)**
- `account_avg_usage`: Historical average for this account
- `account_usage_std`: Historical volatility for this account

**Total: 22 features**

### **3. Simplified Input vs. Documentation Mismatch**

#### **Documentation Shows (ESSENTIAL_FEATURES)**
```python
ESSENTIAL_FEATURES = [
    'usage_lag_1', 'usage_lag_2', 'usage_lag_3',
    'usage_rolling_mean_3', 'month',
    'temperature_max', 'rainfall', 'humidity'
]  # 8 features
```

#### **Actual Model Uses**
```python
ACTUAL_FEATURES = [
    # All 22 features listed above
]  # 22 features
```

### **4. Practical Input Examples**

#### **Example 1: Residential Account**
```json
{
    "account_id": 1001,
    "target_year": 2025,
    "target_month": 2,
    "recent_usage": [25.2, 27.8, 26.5, 28.1, 26.9, 29.3],
    "weather": {
        "temperature_min": 22.8,
        "temperature_max": 30.5,
        "rainfall": 12.4,
        "humidity": 88.0,
        "wind_speed": 8.5
    }
}
```

#### **Example 2: High-Usage Account**
```json
{
    "account_id": 2001,
    "target_year": 2025,
    "target_month": 6,
    "recent_usage": [45.1, 48.2, 46.8, 50.3, 47.5, 52.1],
    "weather": {
        "temperature_min": 24.2,
        "temperature_max": 33.8,
        "rainfall": 5.2,
        "humidity": 82.0,
        "wind_speed": 15.2
    }
}
```

### **5. Data Requirements**

#### **Minimum Data Needed**
- **Historical usage**: At least 3 months (for lag features)
- **Weather data**: Current month weather (or seasonal averages)
- **Account info**: Account ID and basic metadata

#### **Optimal Data**
- **Historical usage**: 6+ months (for better rolling averages)
- **Weather data**: Actual forecast for target month
- **Account history**: Full historical pattern for account-specific features

### **6. Fallback Strategies**

#### **Missing Historical Data**
```python
# If less than 6 months of usage data available:
if len(recent_usage) < 6:
    # Use account average or regional average
    # Reduce confidence score
```

#### **Missing Weather Data**
```python
# If weather forecast not available:
# Use seasonal averages for that month in Sarawak
seasonal_weather = get_seasonal_average(target_month)
```

### **7. API Integration Format**

For FastAPI integration, the input schema would be:

```python
from pydantic import BaseModel
from typing import List

class WeatherData(BaseModel):
    temperature_min: float
    temperature_max: float
    rainfall: float
    humidity: float
    wind_speed: float = 10.0  # Default value

class PredictionRequest(BaseModel):
    account_id: int
    target_year: int
    target_month: int
    recent_usage: List[float]  # Last 6 months
    weather: WeatherData

class PredictionResponse(BaseModel):
    account_id: int
    prediction_month: str
    predicted_usage: float
    change_direction: str
    change_percentage: float
    confidence_score: float
```

### **8. Summary**

**What you actually need to provide:**
1. Account ID
2. Target month/year
3. Last 3-6 months of usage data
4. Weather data for target month

**What the model computes automatically:**
- All 22 engineered features
- Seasonal patterns
- Weather indices
- Account-specific patterns
- Rolling averages and trends

The discrepancy between documentation (8 essential features) and implementation (22 actual features) should be resolved by either:
1. **Retraining** the model with only essential features, or
2. **Updating** the documentation to reflect actual requirements

Which approach would you prefer?
