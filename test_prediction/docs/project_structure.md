# Project Structure Documentation

## Overview

The `test_prediction` directory has been reorganized into a clean, professional structure that follows Python best practices and makes the project easier to navigate and maintain.

## Directory Structure

### Root Level
```
test_prediction/
├── README.md              # Main project documentation
├── requirements.txt       # Python dependencies
├── config.py             # Legacy configuration (kept for compatibility)
```

### Core Directories

#### `/config/` - Configuration Management
```
config/
├── __init__.py           # Package initialization
└── settings.py           # Main project settings and parameters
```
- **Purpose**: Centralized configuration management
- **Contents**: All project parameters, paths, and settings
- **Usage**: `from config import DATA_DIR, N_USERS, etc.`

#### `/data/` - Data Storage
```
data/
├── historical_weather_data.csv      # Real weather data from Sarawak
├── synthetic_water_usage_data.csv   # Generated synthetic usage data
└── plots/                           # Generated visualizations
    ├── usage_analysis.png
    └── weather_correlation.png
```
- **Purpose**: All data files (raw, processed, outputs)
- **Structure**: Organized by data type and processing stage
- **Access**: Via `DATA_DIR` from config

#### `/src/` - Core Source Code
```
src/
├── __init__.py           # Package initialization with main imports
├── data_generator.py     # Data generation utilities
├── feature_engineering.py # Feature engineering pipeline
├── model_trainer.py      # Model training pipeline
└── predictor.py          # Prediction utilities
```
- **Purpose**: Main application logic and reusable modules
- **Design**: Modular, object-oriented design
- **Usage**: `from src import ModelTrainer, Predictor`

#### `/scripts/` - Utility Scripts
```
scripts/
├── __init__.py                      # Package initialization
├── extract_weather_data.py         # Extract weather from database
├── generate_synthetic_usage_data.py # Generate synthetic usage data
├── run_data_generation.py          # Complete data pipeline with validation
├── preview_synthetic_data.py       # Preview data structure
└── run_all.py                      # Master script for all operations
```
- **Purpose**: Standalone scripts for specific tasks
- **Usage**: Run directly from command line
- **Features**: Complete workflows with error handling

#### `/tests/` - Test Suite
```
tests/
├── __init__.py           # Package initialization
├── test_model.py         # Model performance and validation tests
└── compare_features.py   # Feature engineering comparison tests
```
- **Purpose**: Automated testing and validation
- **Coverage**: Model performance, data quality, feature engineering
- **Framework**: Can be extended with pytest or unittest

#### `/notebooks/` - Analysis Notebooks
```
notebooks/
└── (empty - ready for Jupyter notebooks)
```
- **Purpose**: Exploratory data analysis and experimentation
- **Usage**: Jupyter notebooks for interactive analysis
- **Organization**: By analysis type or date

#### `/models/` - Trained Models
```
models/
└── (empty - ready for trained model files)
```
- **Purpose**: Storage for trained ML models
- **Format**: Pickle, joblib, or other serialized formats
- **Versioning**: Can include model versioning scheme

#### `/docs/` - Documentation
```
docs/
└── project_structure.md  # This file
```
- **Purpose**: Project documentation and guides
- **Contents**: Architecture, usage guides, API docs
- **Format**: Markdown for easy reading and maintenance

## Key Improvements

### 1. **Separation of Concerns**
- Scripts vs. reusable modules
- Configuration vs. application logic
- Tests vs. production code

### 2. **Python Package Structure**
- Proper `__init__.py` files
- Clear import paths
- Modular design

### 3. **Professional Organization**
- Standard directory names
- Clear file purposes
- Easy navigation

### 4. **Maintainability**
- Centralized configuration
- Modular components
- Clear dependencies

### 5. **Scalability**
- Room for growth
- Easy to add new features
- Clear extension points

## Usage Examples

### Running Scripts
```bash
# From project root
cd test_prediction

# Run individual scripts
python scripts/extract_weather_data.py
python scripts/generate_synthetic_usage_data.py

# Run complete pipeline
python scripts/run_all.py
```

### Using as Package
```python
# Import configuration
from config import DATA_DIR, N_USERS

# Import core modules
from src import ModelTrainer, Predictor

# Use in your code
trainer = ModelTrainer()
model = trainer.train()
```

### Running Tests
```bash
# Run specific tests
python tests/test_model.py
python tests/compare_features.py

# Or use pytest (if installed)
pytest tests/
```

## Migration Notes

### Files Moved
- `extract_weather_data.py` → `scripts/extract_weather_data.py`
- `generate_synthetic_usage_data.py` → `scripts/generate_synthetic_usage_data.py`
- `run_data_generation.py` → `scripts/run_data_generation.py`
- `preview_synthetic_data.py` → `scripts/preview_synthetic_data.py`
- `test_model.py` → `tests/test_model.py`
- `compare_features.py` → `tests/compare_features.py`
- `config.py` → `config/settings.py` (copied, original kept for compatibility)

### Import Paths
All import paths have been updated to work from the new structure. The original `config.py` is kept at the root level for backward compatibility.

### New Files Added
- `README.md` - Main project documentation
- `__init__.py` files for all packages
- `scripts/run_all.py` - Master script
- `docs/project_structure.md` - This documentation

## Next Steps

1. **Test the new structure** by running scripts from their new locations
2. **Update any external references** to the moved files
3. **Add new features** using the modular structure
4. **Expand documentation** as the project grows
5. **Add more tests** to the test suite

This reorganization provides a solid foundation for the water usage prediction system that can scale and evolve with your needs.
