# Water Usage Prediction System

A machine learning system for predicting water usage patterns in Sarawak, Malaysia, serving 600,000 users.

## Project Structure

```
test_prediction/
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── config.py                # Main configuration (legacy)
│
├── config/                  # Configuration files
│   └── settings.py          # Project settings and parameters
│
├── data/                    # Data files
│   ├── historical_weather_data.csv    # Real weather data (2022-2024)
│   ├── synthetic_water_usage_data.csv # Synthetic usage data
│   └── plots/               # Generated visualizations
│
├── src/                     # Core source code
│   ├── data_generator.py    # Data generation utilities
│   ├── feature_engineering.py # Feature engineering pipeline
│   ├── model_trainer.py     # Model training pipeline
│   └── predictor.py         # Prediction utilities
│
├── scripts/                 # Utility scripts
│   ├── extract_weather_data.py        # Extract weather from database
│   ├── generate_synthetic_usage_data.py # Generate synthetic usage data
│   ├── run_data_generation.py         # Complete data generation pipeline
│   └── preview_synthetic_data.py      # Preview data structure
│
├── tests/                   # Test files
│   ├── test_model.py        # Model testing
│   └── compare_features.py  # Feature comparison tests
│
├── notebooks/               # Jupyter notebooks for analysis
├── models/                  # Trained model files
└── docs/                    # Documentation
```

## Data Overview

### Weather Data
- **Source**: Real historical data from Kuching, Sarawak
- **Period**: January 2022 - December 2024 (36 months)
- **Features**: Temperature, rainfall, humidity, wind speed, pressure, sunshine duration
- **Database**: `pcloudsb.tplinkdns.com:3307/airsarawak`

### Water Usage Data
- **Type**: Synthetic data based on realistic Sarawak patterns
- **Accounts**: 20 diverse user profiles
- **Period**: January 2022 - December 2024 (36 months)
- **Format**: account_id, year, month, usage (m³)

## Quick Start

### 1. Setup Environment
```bash
# Create virtual environment
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
uv pip install -r requirements.txt
```

### 2. Generate Data
```bash
# Extract weather data from database
python scripts/extract_weather_data.py

# Generate synthetic usage data
python scripts/generate_synthetic_usage_data.py

# Or run complete pipeline with validation
python scripts/run_data_generation.py
```

### 3. Train Model
```bash
# Train water usage prediction model
python src/model_trainer.py
```

### 4. Run Tests
```bash
# Test model performance
python tests/test_model.py

# Compare feature strategies
python tests/compare_features.py
```

## Model Architecture

- **Algorithm**: XGBoost (preferred) or Random Forest
- **Input Format**: 20 users × 36 months × n features
- **Features**: Past usage + environmental data
- **Output**: Prediction month, predicted usage, change direction, change percentage, confidence score

## Key Features

- ✅ Real weather data from Sarawak
- ✅ Synthetic usage data with realistic patterns
- ✅ Time series modeling (36 months)
- ✅ Multiple user profiles (20 accounts)
- ✅ Environmental correlation modeling
- ✅ Seasonal pattern recognition
- ✅ Growth trend analysis

## Configuration

Key parameters in `config/settings.py`:
- `N_USERS`: Number of user accounts (20)
- `N_MONTHS`: Time series length (36)
- `USAGE_PATTERNS`: Realistic usage parameters for Sarawak
- `ESSENTIAL_FEATURES`: Lean feature set for optimal performance

## Development

This project follows a clean architecture with:
- Separation of concerns (data, models, scripts, tests)
- Modular design for easy maintenance
- Comprehensive testing and validation
- Clear documentation and examples

## Next Steps

1. Train and validate the prediction model
2. Implement the FastAPI prediction service
3. Add batch processing capabilities
4. Integrate with the main water utility system
