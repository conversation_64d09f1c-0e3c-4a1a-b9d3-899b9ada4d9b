# Configuration for Water Usage Prediction Test
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
MODELS_DIR = PROJECT_ROOT / "models"
SRC_DIR = PROJECT_ROOT / "src"
SCRIPTS_DIR = PROJECT_ROOT / "scripts"
NOTEBOOKS_DIR = PROJECT_ROOT / "notebooks"
TESTS_DIR = PROJECT_ROOT / "tests"
DOCS_DIR = PROJECT_ROOT / "docs"
CONFIG_DIR = PROJECT_ROOT / "config"

# Create directories if they don't exist
for directory in [DATA_DIR, MODELS_DIR, SRC_DIR, SCRIPTS_DIR, NOTEBOOKS_DIR, TESTS_DIR, DOCS_DIR, CONFIG_DIR]:
    directory.mkdir(exist_ok=True)

# Data generation parameters
N_USERS = 20
N_MONTHS = 36

# Essential features only (lean approach)
ESSENTIAL_FEATURES = [
    'usage_lag_1',           # Last month usage (most important)
    'usage_lag_2',           # 2 months ago
    'usage_lag_3',           # 3 months ago
    'usage_rolling_mean_3',  # 3-month average
    'month',                 # Basic seasonal patterns
    'temperature_max',       # Heat drives usage
    'rainfall',              # Direct rainfall measurement
    'humidity'               # Humidity affects water usage in tropical climate
]

# Raw data features to generate
RAW_FEATURES = [
    'usage',  # Target variable
    'temperature_min',
    'temperature_max',
    'rainfall',
    'humidity',
    'wind_speed',
    'surface_pressure',
    'sunshine_duration',
    'month',
    'quarter',
    'days_in_month'
]

# Feature selection strategy
FEATURE_STRATEGY = "lean"  # "lean" or "full"

# Model parameters
MODEL_CONFIG = {
    'n_estimators': 100,
    'max_depth': 6,
    'learning_rate': 0.1,
    'subsample': 0.8,
    'colsample_bytree': 0.8,
    'random_state': 42
}

# Training parameters
TRAIN_TEST_SPLIT = 0.8  # 80% for training, 20% for testing
VALIDATION_SPLIT = 0.2  # 20% of training data for validation

# Performance thresholds
PERFORMANCE_THRESHOLD = {
    'r2_score': 0.75,
    'mae': 10.0  # Mean Absolute Error in m³
}

# Sarawak climate patterns (for realistic synthetic data)
SARAWAK_CLIMATE = {
    'temperature': {'min': 24, 'max': 32, 'avg': 28},
    'rainfall': {'dry_season': 50, 'wet_season': 300, 'peak_wet': 500},
    'humidity': {'min': 70, 'max': 90, 'avg': 80},
    'wind_speed': {'min': 5, 'max': 20, 'avg': 12}
}

# Water usage patterns (realistic ranges for Sarawak)
USAGE_PATTERNS = {
    'residential_avg': 25,  # m³ per month
    'residential_std': 8,
    'seasonal_factor': 0.3,  # 30% increase during dry season
    'trend_factor': 0.02,   # 2% annual growth
    'user_variation': 0.4,  # 40% variation between users
    'monthly_noise': 0.15   # 15% random monthly variation
}

# Rainfall categorization thresholds (MONTHLY data)
# Based on ACTUAL Sarawak data analysis (36 monthly records, 4.24-36.75mm range)
# Note: This appears to be monthly averages, not daily totals
RAINFALL_THRESHOLDS = {
    'dry': 10.1,     # < 10.1mm/month = dry month (25th percentile)
    'moderate': 21.3, # 10.1-21.3mm/month = moderate month (25th-75th percentile)
    'wet': 21.3      # > 21.3mm/month = wet month (75th percentile)
}

# For monthly aggregation (if needed)
MONTHLY_RAINFALL_THRESHOLDS = {
    'dry': 150,      # < 150mm/month = dry month (5mm/day avg)
    'moderate': 450, # 150-450mm/month = moderate (5-15mm/day avg)
    'wet': 450       # > 450mm/month = wet month (>15mm/day avg)
}