# Extract all historical weather data from database
import mysql.connector
import pandas as pd
import sys
import os

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_DIR

# Database connection parameters
DB_CONFIG = {
    'host': 'pcloudsb.tplinkdns.com',
    'user': 'server',
    'password': 'pCloud@8866',
    'port': 3307,
    'database': 'airsarawak'
}

def connect_to_database():
    """Connect to the database"""
    try:
        print("Connecting to database...")
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            port=DB_CONFIG['port'],
            database=DB_CONFIG['database'],
            connection_timeout=30,
            autocommit=True
        )
        print("✓ Connected to database")
        return connection
    except mysql.connector.Error as e:
        print(f"✗ Database connection failed: {e}")
        return None

def extract_weather_data(connection):
    """Extract all data from historical_weather table"""
    try:
        print("Extracting weather data...")
        cursor = connection.cursor()
        
        # Get all data
        cursor.execute("SELECT * FROM historical_weather")
        all_data = cursor.fetchall()
        
        # Get column names
        column_names = [desc[0] for desc in cursor.description]
        print(f"✓ Retrieved {len(all_data)} records")
        
        # Convert to DataFrame
        df = pd.DataFrame(all_data, columns=column_names)
        cursor.close()
        return df
        
    except mysql.connector.Error as e:
        print(f"✗ Error extracting data: {e}")
        return None

def save_weather_data(df, filename="historical_weather_data.csv"):
    """Save weather data to CSV file"""
    if df is None or len(df) == 0:
        print("✗ No data to save")
        return None

    filepath = DATA_DIR / filename
    df.to_csv(filepath, index=False)
    print(f"✓ Weather data saved to: {filepath}")
    return filepath

def main():
    """Extract weather data and save to CSV"""
    print("HISTORICAL WEATHER DATA EXTRACTION")
    
    # Connect to database
    connection = connect_to_database()
    if not connection:
        print("Cannot proceed without database connection")
        return

    try:
        # Extract data
        df = extract_weather_data(connection)

        if df is not None:
            # Save data
            filepath = save_weather_data(df)
            print(f"✓ Extracted and saved {len(df)} records")
    finally:
        connection.close()
        print("✓ Database connection closed")

if __name__ == "__main__":
    main()
