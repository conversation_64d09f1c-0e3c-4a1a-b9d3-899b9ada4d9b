# Generate synthetic water usage data matching historical weather period
import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_DIR, N_USERS, USAGE_PATTERNS, SARAWAK_CLIMATE

def load_weather_data():
    """Load the historical weather data"""
    weather_file = DATA_DIR / "historical_weather_data.csv"
    if not weather_file.exists():
        raise FileNotFoundError(f"Weather data not found at {weather_file}")

    df = pd.read_csv(weather_file)
    print(f"✓ Loaded weather data: {len(df)} records from {df['year'].min()}-{df['month'].min():02d} to {df['year'].max()}-{df['month'].max():02d}")
    return df

def generate_user_profiles(n_users):
    """Generate diverse user profiles with different characteristics"""
    np.random.seed(42)  # For reproducible results

    profiles = []
    for user_id in range(1, n_users + 1):
        # Base usage varies between users (15-40 m³/month)
        base_usage = np.random.normal(
            USAGE_PATTERNS['residential_avg'],
            USAGE_PATTERNS['residential_avg'] * USAGE_PATTERNS['user_variation']
        )
        base_usage = max(15, min(40, base_usage))  # Clamp to reasonable range

        # Different sensitivity to weather
        temp_sensitivity = np.random.uniform(0.5, 2.0)  # How much temperature affects usage
        rain_sensitivity = np.random.uniform(-0.3, 0.1)  # Negative: less usage when more rain

        # Different growth trends
        growth_rate = np.random.normal(USAGE_PATTERNS['trend_factor'], 0.01)

        profiles.append({
            'user_id': user_id,
            'base_usage': base_usage,
            'temp_sensitivity': temp_sensitivity,
            'rain_sensitivity': rain_sensitivity,
            'growth_rate': growth_rate
        })

    return profiles

def calculate_seasonal_factor(month, temperature_max, rainfall):
    """Calculate seasonal usage factor based on weather"""
    # Dry season (higher temperatures, lower rainfall) = higher usage
    # Wet season (lower temperatures, higher rainfall) = lower usage

    # Temperature effect (normalized to 0-1 scale)
    temp_factor = (temperature_max - 28) / 4  # Normalize around 28°C

    # Rainfall effect (inverse relationship)
    rain_factor = -(rainfall - 15) / 20  # Normalize around 15mm

    # Combine effects
    seasonal_factor = 1 + 0.2 * temp_factor + 0.1 * rain_factor

    # Clamp to reasonable range
    return max(0.7, min(1.4, seasonal_factor))

def generate_synthetic_usage_data(weather_df, user_profiles):
    """Generate synthetic water usage data for all users and time periods"""
    usage_data = []

    for profile in user_profiles:
        user_id = profile['user_id']
        base_usage = profile['base_usage']
        temp_sensitivity = profile['temp_sensitivity']
        rain_sensitivity = profile['rain_sensitivity']
        growth_rate = profile['growth_rate']

        print(f"Generating data for User {user_id} (base: {base_usage:.1f} m³)")

        for _, weather_row in weather_df.iterrows():
            year = weather_row['year']
            month = weather_row['month']
            temp_max = weather_row['temperature_max']
            rainfall = weather_row['rainfall']
            humidity = weather_row['humidity']

            # Calculate months since start for trend
            months_since_start = (year - 2022) * 12 + (month - 1)

            # Base usage with growth trend
            current_base = base_usage * (1 + growth_rate * months_since_start / 12)

            # Seasonal adjustment based on weather
            seasonal_factor = calculate_seasonal_factor(month, temp_max, rainfall)

            # Weather-specific adjustments
            temp_adjustment = temp_sensitivity * (temp_max - 29) / 10  # Normalized
            rain_adjustment = rain_sensitivity * (rainfall - 15) / 20  # Normalized

            # Calculate final usage
            usage = current_base * seasonal_factor * (1 + temp_adjustment + rain_adjustment)

            # Add monthly noise
            noise_factor = np.random.normal(1, USAGE_PATTERNS['monthly_noise'])
            usage *= noise_factor

            # Ensure positive usage
            usage = max(5, usage)

            usage_data.append({
                'account_id': user_id,
                'year': year,
                'month': month,
                'usage': round(usage, 2)
            })

    return pd.DataFrame(usage_data)

def save_usage_data(df, filename="synthetic_water_usage_data.csv"):
    """Save synthetic usage data to CSV file"""
    filepath = DATA_DIR / filename
    df.to_csv(filepath, index=False)
    print(f"✓ Synthetic usage data saved to: {filepath}")
    return filepath

def generate_summary_stats(df):
    """Generate and display summary statistics"""
    print("\n" + "="*50)
    print("SYNTHETIC DATA SUMMARY")
    print("="*50)

    print(f"Total records: {len(df)}")
    print(f"Accounts: {df['account_id'].nunique()}")
    print(f"Time period: {df['year'].min()}-{df['month'].min():02d} to {df['year'].max()}-{df['month'].max():02d}")
    print(f"Total months: {len(df) // df['account_id'].nunique()}")

    print(f"\nUsage Statistics:")
    print(f"  Mean: {df['usage'].mean():.2f} m³")
    print(f"  Std:  {df['usage'].std():.2f} m³")
    print(f"  Min:  {df['usage'].min():.2f} m³")
    print(f"  Max:  {df['usage'].max():.2f} m³")

    print(f"\nPer-account averages:")
    user_stats = df.groupby('account_id')['usage'].agg(['mean', 'std']).round(2)
    print(user_stats.head(10))

    print(f"\nMonthly patterns:")
    monthly_stats = df.groupby('month')['usage'].mean().round(2)
    print(monthly_stats)

def main():
    """Generate synthetic water usage data matching weather period"""
    print("SYNTHETIC WATER USAGE DATA GENERATION")
    print("="*50)

    try:
        # Load weather data
        weather_df = load_weather_data()

        # Generate user profiles
        print(f"\nGenerating {N_USERS} user profiles...")
        user_profiles = generate_user_profiles(N_USERS)

        # Generate synthetic usage data
        print(f"\nGenerating synthetic usage data...")
        usage_df = generate_synthetic_usage_data(weather_df, user_profiles)

        # Save data
        filepath = save_usage_data(usage_df)

        # Generate summary
        generate_summary_stats(usage_df)

        print(f"\n✓ Successfully generated synthetic data for {len(usage_df)} records")
        print(f"✓ Data saved to: {filepath}")

    except Exception as e:
        print(f"✗ Error generating synthetic data: {e}")
        return False

    return True

if __name__ == "__main__":
    main()
