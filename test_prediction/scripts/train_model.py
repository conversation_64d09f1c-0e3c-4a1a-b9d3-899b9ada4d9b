#!/usr/bin/env python3
"""
Complete XGBoost Model Training Script for Water Usage Prediction

This script implements the complete model training flow:
1. Load and merge usage + weather data
2. Create time series features (lags, rolling averages)
3. Engineer environmental features
4. Train XGBoost with time series validation
5. Generate predictions with confidence scores
6. Save trained model
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    """Run complete model training pipeline"""
    print("WATER USAGE PREDICTION - XGBOOST MODEL TRAINING")
    print("="*60)
    
    try:
        # Import and run the model pipeline
        from src.model_pipeline import WaterUsagePredictionPipeline
        
        # Initialize pipeline
        pipeline = WaterUsagePredictionPipeline()
        
        print("🚀 Starting model training pipeline...")
        
        # Step 1: Load data
        print("\n1️⃣ LOADING DATA")
        print("-" * 30)
        df = pipeline.load_data()
        
        # Step 2: Create time series features
        print("\n2️⃣ CREATING TIME SERIES FEATURES")
        print("-" * 30)
        df = pipeline.create_time_series_features(df)
        
        # Step 3: Engineer features
        print("\n3️⃣ ENGINEERING FEATURES")
        print("-" * 30)
        df = pipeline.engineer_features(df)
        
        # Step 4: Prepare training data
        print("\n4️⃣ PREPARING TRAINING DATA")
        print("-" * 30)
        X, y, df_clean = pipeline.prepare_training_data(df)
        
        # Step 5: Train model
        print("\n5️⃣ TRAINING XGBOOST MODEL")
        print("-" * 30)
        metrics = pipeline.train_model(X, y)
        
        # Step 6: Save model
        print("\n6️⃣ SAVING MODEL")
        print("-" * 30)
        model_path = pipeline.save_model()
        
        # Step 7: Test prediction functionality
        print("\n7️⃣ TESTING PREDICTIONS")
        print("-" * 30)
        
        from src.water_usage_predictor import WaterUsagePredictor
        
        # Initialize predictor with trained model
        predictor = WaterUsagePredictor(model_path)
        
        # Load historical data for testing
        historical_data = predictor.load_historical_data()
        
        # Test single prediction
        test_prediction = predictor.predict_single_account(
            account_id=1,
            target_year=2025,
            target_month=1,
            historical_data=historical_data
        )
        
        print("✅ Sample prediction test:")
        print(f"   Account: {test_prediction['account_id']}")
        print(f"   Month: {test_prediction['prediction_month']}")
        print(f"   Predicted Usage: {test_prediction['predicted_usage']} m³")
        print(f"   Change: {test_prediction['change_direction']} ({test_prediction['change_percentage']}%)")
        print(f"   Confidence: {test_prediction['confidence_score']}")
        
        # Test batch prediction
        batch_predictions = predictor.predict_batch(
            account_ids=[1, 2, 3, 4, 5],
            target_year=2025,
            target_month=1,
            historical_data=historical_data
        )
        
        print(f"\n✅ Batch prediction test ({len(batch_predictions)} accounts):")
        for pred in batch_predictions[:3]:  # Show first 3
            print(f"   Account {pred['account_id']}: {pred['predicted_usage']} m³ ({pred['change_direction']})")
        
        # Final summary
        print("\n" + "="*60)
        print("🎉 MODEL TRAINING COMPLETE!")
        print("="*60)
        print(f"✅ Model saved to: {model_path}")
        print(f"✅ Training Performance:")
        print(f"   - Training MAE: {metrics['train_mae']:.2f} m³")
        print(f"   - Testing MAE: {metrics['test_mae']:.2f} m³")
        print(f"   - Training R²: {metrics['train_r2']:.3f}")
        print(f"   - Testing R²: {metrics['test_r2']:.3f}")
        print(f"✅ Prediction System: Ready for deployment")
        print(f"✅ Features: {len(pipeline.feature_columns)} engineered features")
        print(f"✅ Training Data: {len(X)} samples")
        print("="*60)
        
        # Save training summary
        summary = {
            'model_path': str(model_path),
            'training_metrics': metrics,
            'feature_count': len(pipeline.feature_columns),
            'training_samples': len(X),
            'feature_columns': pipeline.feature_columns,
            'training_timestamp': pd.Timestamp.now().isoformat()
        }
        
        summary_path = model_path.parent / "training_summary.json"
        import json
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"📊 Training summary saved to: {summary_path}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TRAINING FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
