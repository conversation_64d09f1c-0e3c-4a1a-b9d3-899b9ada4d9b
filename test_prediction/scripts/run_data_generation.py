# Run synthetic data generation and validation
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import sys
import os

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_DIR, N_USERS

def validate_generated_data():
    """Validate the generated synthetic data"""
    print("\nVALIDATING GENERATED DATA")
    print("="*40)

    # Load the generated data
    usage_file = DATA_DIR / "synthetic_water_usage_data.csv"
    if not usage_file.exists():
        print("✗ Synthetic usage data not found. Run generation first.")
        return False

    df = pd.read_csv(usage_file)

    # Basic validation checks
    print("Basic Validation Checks:")

    # Check record count
    expected_records = N_USERS * 36  # 20 users × 36 months
    actual_records = len(df)
    print(f"  Records: {actual_records} (expected: {expected_records}) {'✓' if actual_records == expected_records else '✗'}")

    # Check accounts
    unique_accounts = df['account_id'].nunique()
    print(f"  Accounts: {unique_accounts} (expected: {N_USERS}) {'✓' if unique_accounts == N_USERS else '✗'}")

    # Check time period
    min_date = f"{df['year'].min()}-{df['month'].min():02d}"
    max_date = f"{df['year'].max()}-{df['month'].max():02d}"
    print(f"  Time period: {min_date} to {max_date} {'✓' if min_date == '2022-01' and max_date == '2024-12' else '✗'}")

    # Check for missing values
    missing_values = df.isnull().sum().sum()
    print(f"  Missing values: {missing_values} {'✓' if missing_values == 0 else '✗'}")

    # Check usage range
    min_usage = df['usage'].min()
    max_usage = df['usage'].max()
    print(f"  Usage range: {min_usage:.2f} - {max_usage:.2f} m³ {'✓' if 5 <= min_usage and max_usage <= 60 else '✗'}")

    return True

def create_data_visualizations():
    """Create visualizations to verify data quality"""
    print("\nCREATING DATA VISUALIZATIONS")
    print("="*40)

    # Load data
    usage_file = DATA_DIR / "synthetic_water_usage_data.csv"
    df = pd.read_csv(usage_file)

    # Create plots directory
    plots_dir = DATA_DIR / "plots"
    plots_dir.mkdir(exist_ok=True)

    # Set style
    plt.style.use('default')
    sns.set_palette("husl")

    # 1. Usage distribution
    plt.figure(figsize=(12, 8))

    plt.subplot(2, 2, 1)
    plt.hist(df['usage'], bins=30, alpha=0.7, edgecolor='black')
    plt.title('Water Usage Distribution')
    plt.xlabel('Usage (m³)')
    plt.ylabel('Frequency')

    # 2. Monthly patterns
    plt.subplot(2, 2, 2)
    monthly_avg = df.groupby('month')['usage'].mean()
    plt.plot(monthly_avg.index, monthly_avg.values, marker='o')
    plt.title('Average Usage by Month')
    plt.xlabel('Month')
    plt.ylabel('Average Usage (m³)')
    plt.xticks(range(1, 13))

    # 3. Account variation
    plt.subplot(2, 2, 3)
    account_avg = df.groupby('account_id')['usage'].mean()
    plt.bar(account_avg.index, account_avg.values)
    plt.title('Average Usage by Account')
    plt.xlabel('Account ID')
    plt.ylabel('Average Usage (m³)')

    # 4. Time series for sample accounts
    plt.subplot(2, 2, 4)
    sample_accounts = [1, 5, 10, 15, 20]
    for account_id in sample_accounts:
        account_data = df[df['account_id'] == account_id].sort_values(['year', 'month'])
        account_data['date'] = pd.to_datetime(account_data[['year', 'month']].assign(day=1))
        plt.plot(account_data['date'], account_data['usage'], label=f'Account {account_id}', alpha=0.7)

    plt.title('Usage Time Series (Sample Accounts)')
    plt.xlabel('Date')
    plt.ylabel('Usage (m³)')
    plt.legend()
    plt.xticks(rotation=45)

    plt.tight_layout()
    plot_file = plots_dir / "usage_analysis.png"
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"✓ Usage analysis plot saved to: {plot_file}")

    print(f"✓ Usage analysis plots created")

def main():
    """Run data generation and validation"""
    print("SYNTHETIC DATA GENERATION AND VALIDATION")
    print("="*50)

    # Import and run data generation
    try:
        from generate_synthetic_usage_data import main as generate_data
        success = generate_data()

        if not success:
            print("✗ Data generation failed")
            return

        # Validate the generated data
        validate_generated_data()

        # Create visualizations
        create_data_visualizations()

        print("\n" + "="*50)
        print("✓ SYNTHETIC DATA GENERATION COMPLETE")
        print("✓ Data validation passed")
        print("✓ Visualizations created")
        print("="*50)

    except Exception as e:
        print(f"✗ Error in data generation pipeline: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
