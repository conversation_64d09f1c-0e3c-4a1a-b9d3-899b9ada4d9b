# Preview what the synthetic data will look like
import pandas as pd
import numpy as np
import sys
import os

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DATA_DIR, N_USERS, USAGE_PATTERNS

def preview_weather_data():
    """Show preview of the weather data we'll use"""
    print("WEATHER DATA PREVIEW")
    print("="*40)

    weather_file = DATA_DIR / "historical_weather_data.csv"
    if not weather_file.exists():
        print("✗ Weather data not found")
        return None

    df = pd.read_csv(weather_file)

    print(f"Time period: {df['year'].min()}-{df['month'].min():02d} to {df['year'].max()}-{df['month'].max():02d}")
    print(f"Total months: {len(df)}")
    print(f"Location: {df['city'].iloc[0]}")

    print(f"\nWeather ranges:")
    print(f"  Temperature: {df['temperature_min'].min():.1f}°C - {df['temperature_max'].max():.1f}°C")
    print(f"  Rainfall: {df['rainfall'].min():.1f}mm - {df['rainfall'].max():.1f}mm")
    print(f"  Humidity: {df['humidity'].min():.1f}% - {df['humidity'].max():.1f}%")

    print(f"\nFirst 5 records:")
    print(df[['year', 'month', 'temperature_max', 'rainfall', 'humidity']].head())

    return df

def preview_user_profiles():
    """Show what user profiles will look like"""
    print("\nUSER PROFILES PREVIEW")
    print("="*40)

    np.random.seed(42)  # Same seed as in generator

    profiles = []
    for user_id in range(1, min(6, N_USERS + 1)):  # Show first 5 users
        base_usage = np.random.normal(
            USAGE_PATTERNS['residential_avg'],
            USAGE_PATTERNS['residential_avg'] * USAGE_PATTERNS['user_variation']
        )
        base_usage = max(15, min(40, base_usage))

        temp_sensitivity = np.random.uniform(0.5, 2.0)
        rain_sensitivity = np.random.uniform(-0.3, 0.1)
        growth_rate = np.random.normal(USAGE_PATTERNS['trend_factor'], 0.01)

        profiles.append({
            'user_id': user_id,
            'base_usage': round(base_usage, 1),
            'temp_sensitivity': round(temp_sensitivity, 2),
            'rain_sensitivity': round(rain_sensitivity, 2),
            'growth_rate': round(growth_rate * 100, 2)  # Show as percentage
        })

    df_profiles = pd.DataFrame(profiles)
    print(df_profiles.to_string(index=False))

    print(f"\nProfile explanation:")
    print(f"  base_usage: Average monthly usage (m³)")
    print(f"  temp_sensitivity: How much temperature affects usage (multiplier)")
    print(f"  rain_sensitivity: How rainfall affects usage (negative = less usage when more rain)")
    print(f"  growth_rate: Annual usage growth (%)")

def preview_expected_output():
    """Show what the final synthetic data structure will look like"""
    print("\nEXPECTED OUTPUT STRUCTURE")
    print("="*40)

    print(f"Total records expected: {N_USERS} users × 36 months = {N_USERS * 36} records")

    # Sample data structure
    sample_data = {
        'account_id': [1, 1, 1, 2, 2, 2],
        'year': [2022, 2022, 2022, 2022, 2022, 2022],
        'month': [1, 2, 3, 1, 2, 3],
        'usage': [23.45, 25.12, 22.89, 31.67, 33.21, 29.45]
    }

    df_sample = pd.DataFrame(sample_data)
    print("Sample data structure:")
    print(df_sample.to_string(index=False))

    print(f"\nColumns included:")
    print(f"  account_id: Account identifier (1-{N_USERS})")
    print(f"  year, month: Time period (2022-01 to 2024-12)")
    print(f"  usage: Synthetic water usage (m³)")
    print(f"\nNote: Weather data is kept separate and will be joined during model training")

def main():
    """Preview the synthetic data generation"""
    print("SYNTHETIC DATA GENERATION PREVIEW")
    print("="*50)

    # Preview weather data
    weather_df = preview_weather_data()

    if weather_df is not None:
        # Preview user profiles
        preview_user_profiles()

        # Preview expected output
        preview_expected_output()

        print(f"\n" + "="*50)
        print("READY TO GENERATE SYNTHETIC DATA")
        print("="*50)
        print(f"✓ Weather data loaded: {len(weather_df)} months")
        print(f"✓ Will generate data for {N_USERS} users")
        print(f"✓ Total records to create: {N_USERS * len(weather_df)}")
        print(f"✓ Time period: 2022-01 to 2024-12")

        print(f"\nTo generate the data, run:")
        print(f"  python generate_synthetic_usage_data.py")
        print(f"  or")
        print(f"  python run_data_generation.py  # (includes validation)")

if __name__ == "__main__":
    main()
