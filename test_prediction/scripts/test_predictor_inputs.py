#!/usr/bin/env python3
"""
Test script to demonstrate exact input requirements for the water usage predictor
"""

import sys
import os
import pandas as pd

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_predictor_inputs():
    """Test what inputs are actually required for prediction"""
    print("WATER USAGE PREDICTOR - INPUT REQUIREMENTS TEST")
    print("="*60)

    try:
        from src.water_usage_predictor import WaterUsagePredictor

        # Load the trained model
        from config import MODELS_DIR
        model_path = MODELS_DIR / "water_usage_xgboost_model.pkl"
        predictor = WaterUsagePredictor(model_path)

        # Load historical data (this simulates what would be in your database)
        historical_data = predictor.load_historical_data()
        print(f"✓ Loaded historical data: {len(historical_data)} records")

        # Show what features the model actually expects
        print(f"\n📋 MODEL EXPECTS {len(predictor.feature_columns)} FEATURES:")
        for i, feature in enumerate(predictor.feature_columns, 1):
            print(f"  {i:2d}. {feature}")

        # Test 1: Minimal input example
        print(f"\n🧪 TEST 1: MINIMAL INPUT EXAMPLE")
        print("-" * 40)

        # What you need to provide:
        input_data = {
            "account_id": 1,
            "target_year": 2025,
            "target_month": 3,
            "recent_usage": [30.5, 32.1, 28.9, 31.2, 29.8, 33.4],  # Last 6 months
            "weather": {
                "temperature_min": 23.2,
                "temperature_max": 31.8,
                "rainfall": 15.5,
                "humidity": 87.0,
                "wind_speed": 9.2
            }
        }

        print("Input you provide:")
        print(f"  Account ID: {input_data['account_id']}")
        print(f"  Target: {input_data['target_year']}-{input_data['target_month']:02d}")
        print(f"  Recent usage: {input_data['recent_usage']}")
        print(f"  Weather: {input_data['weather']}")

        # Make prediction
        prediction = predictor.predict_single_account(
            account_id=input_data["account_id"],
            target_year=input_data["target_year"],
            target_month=input_data["target_month"],
            historical_data=historical_data
        )

        print(f"\n📊 PREDICTION RESULT:")
        print(f"  Predicted Usage: {prediction['predicted_usage']} m³")
        print(f"  Change Direction: {prediction['change_direction']}")
        print(f"  Change Percentage: {prediction['change_percentage']}%")
        print(f"  Confidence: {prediction['confidence_score']}")

        # Test 2: Show feature computation
        print(f"\n🔧 TEST 2: FEATURE COMPUTATION EXAMPLE")
        print("-" * 40)

        # Show how features are computed from inputs
        features = predictor.prepare_prediction_features(
            account_id=1,
            target_year=2025,
            target_month=3,
            historical_data=historical_data
        )

        print("Features computed from your inputs:")
        for feature_name in predictor.feature_columns[:10]:  # Show first 10
            value = features.get(feature_name, "N/A")
            print(f"  {feature_name}: {value}")
        print(f"  ... and {len(predictor.feature_columns) - 10} more features")

        # Test 3: Batch prediction example
        print(f"\n🔄 TEST 3: BATCH PREDICTION EXAMPLE")
        print("-" * 40)

        batch_predictions = predictor.predict_batch(
            account_ids=[1, 2, 3],
            target_year=2025,
            target_month=3,
            historical_data=historical_data
        )

        print("Batch prediction results:")
        for pred in batch_predictions:
            print(f"  Account {pred['account_id']}: {pred['predicted_usage']:.1f} m³ ({pred['change_direction']})")

        # Test 4: Show what happens with missing data
        print(f"\n⚠️  TEST 4: HANDLING MISSING DATA")
        print("-" * 40)

        # Test with account that has limited history
        try:
            limited_prediction = predictor.predict_single_account(
                account_id=999,  # Non-existent account
                target_year=2025,
                target_month=3,
                historical_data=historical_data
            )
            print("✓ Model handles missing account data gracefully")
        except Exception as e:
            print(f"⚠️  Missing account handling: {e}")

        # Summary
        print(f"\n" + "="*60)
        print("📋 SUMMARY: WHAT YOU NEED TO PROVIDE")
        print("="*60)
        print("✅ REQUIRED INPUTS:")
        print("   1. account_id (int)")
        print("   2. target_year (int)")
        print("   3. target_month (int, 1-12)")
        print("   4. recent_usage (list of 3-6 recent monthly values)")
        print("   5. weather data for target month:")
        print("      - temperature_min (°C)")
        print("      - temperature_max (°C)")
        print("      - rainfall (mm)")
        print("      - humidity (%)")
        print("      - wind_speed (km/h, optional)")

        print(f"\n🤖 MODEL AUTOMATICALLY COMPUTES:")
        print(f"   - All {len(predictor.feature_columns)} engineered features")
        print("   - Lag features from usage history")
        print("   - Rolling averages and trends")
        print("   - Weather indices and categories")
        print("   - Seasonal patterns")
        print("   - Account-specific patterns")

        print(f"\n📤 OUTPUT FORMAT:")
        print("   - prediction_month (YYYY-MM)")
        print("   - predicted_usage (m³)")
        print("   - change_direction (up/down/stable)")
        print("   - change_percentage (%)")
        print("   - confidence_score (0.0-1.0)")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_predictor_inputs()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}")
