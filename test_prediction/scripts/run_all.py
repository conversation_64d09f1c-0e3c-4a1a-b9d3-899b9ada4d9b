#!/usr/bin/env python3
"""
Master script to run all data generation and validation steps.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    """Run all data generation steps"""
    print("WATER USAGE PREDICTION - DATA PIPELINE")
    print("="*50)
    
    scripts_dir = Path(__file__).parent
    
    # Step 1: Extract weather data
    print("\n1. EXTRACTING WEATHER DATA")
    print("-" * 30)
    try:
        from extract_weather_data import main as extract_weather
        extract_weather()
    except Exception as e:
        print(f"✗ Weather extraction failed: {e}")
        return False
    
    # Step 2: Generate synthetic usage data
    print("\n2. GENERATING SYNTHETIC USAGE DATA")
    print("-" * 30)
    try:
        from generate_synthetic_usage_data import main as generate_usage
        success = generate_usage()
        if not success:
            print("✗ Usage data generation failed")
            return False
    except Exception as e:
        print(f"✗ Usage data generation failed: {e}")
        return False
    
    # Step 3: Run validation
    print("\n3. RUNNING VALIDATION")
    print("-" * 30)
    try:
        from run_data_generation import validate_generated_data
        validate_generated_data()
    except Exception as e:
        print(f"✗ Validation failed: {e}")
        return False
    
    print("\n" + "="*50)
    print("✅ ALL STEPS COMPLETED SUCCESSFULLY")
    print("✅ Data is ready for model training")
    print("="*50)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
