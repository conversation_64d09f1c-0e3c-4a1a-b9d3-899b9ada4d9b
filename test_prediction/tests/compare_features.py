# Compare Lean vs Full Feature Approaches
import sys
from pathlib import Path
import pandas as pd
import numpy as np

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from data_generator import WaterUsageDataGenerator
from feature_engineering import WaterUsageFeatureEngineer
from model_trainer import WaterUsageModelTrainer
from config import *

def compare_feature_strategies():
    """Compare lean vs full feature strategies"""
    print("=" * 60)
    print("FEATURE STRATEGY COMPARISON")
    print("=" * 60)
    
    # Generate data once
    print("\n1. GENERATING DATA")
    print("-" * 30)
    generator = WaterUsageDataGenerator(n_users=N_USERS, n_months=N_MONTHS)
    df_raw = generator.generate_dataset()
    
    # Feature engineering
    engineer = WaterUsageFeatureEngineer()
    df_features = engineer.engineer_features(df_raw)
    
    results = {}
    
    # Test both strategies
    for strategy in ["lean", "full"]:
        print(f"\n2. TESTING '{strategy.upper()}' STRATEGY")
        print("-" * 30)
        
        # Prepare data with specific strategy
        X, y, feature_cols = engineer.prepare_model_data(df_features, strategy=strategy)
        
        print(f"Features used: {len(feature_cols)}")
        print(f"Sample size: {len(X)}")
        print(f"Features: {feature_cols}")
        
        # Train model
        trainer = WaterUsageModelTrainer()
        X_train, X_test, y_train, y_test = trainer.create_time_series_split(X, y, test_size=0.2)
        
        trainer.train_model(X_train, y_train)
        metrics = trainer.evaluate_model(X_test, y_test)
        
        # Store results
        results[strategy] = {
            'n_features': len(feature_cols),
            'n_samples': len(X),
            'features': feature_cols,
            'r2_score': metrics['r2_score'],
            'mae': metrics['mae'],
            'rmse': metrics['rmse'],
            'mape': metrics['mape']
        }
        
        print(f"✓ R² Score: {metrics['r2_score']:.3f}")
        print(f"✓ MAE: {metrics['mae']:.3f} m³")
        print(f"✓ RMSE: {metrics['rmse']:.3f} m³")
        print(f"✓ MAPE: {metrics['mape']:.2f}%")
    
    # Compare results
    print(f"\n3. COMPARISON RESULTS")
    print("-" * 30)
    
    lean_results = results['lean']
    full_results = results['full']
    
    print(f"{'Metric':<15} {'Lean':<12} {'Full':<12} {'Winner':<10}")
    print("-" * 50)
    
    # Feature count
    print(f"{'Features':<15} {lean_results['n_features']:<12} {full_results['n_features']:<12} {'Lean' if lean_results['n_features'] < full_results['n_features'] else 'Full':<10}")
    
    # R² Score (higher is better)
    r2_winner = 'Lean' if lean_results['r2_score'] > full_results['r2_score'] else 'Full'
    print(f"{'R² Score':<15} {lean_results['r2_score']:<12.3f} {full_results['r2_score']:<12.3f} {r2_winner:<10}")
    
    # MAE (lower is better)
    mae_winner = 'Lean' if lean_results['mae'] < full_results['mae'] else 'Full'
    print(f"{'MAE':<15} {lean_results['mae']:<12.3f} {full_results['mae']:<12.3f} {mae_winner:<10}")
    
    # RMSE (lower is better)
    rmse_winner = 'Lean' if lean_results['rmse'] < full_results['rmse'] else 'Full'
    print(f"{'RMSE':<15} {lean_results['rmse']:<12.3f} {full_results['rmse']:<12.3f} {rmse_winner:<10}")
    
    # MAPE (lower is better)
    mape_winner = 'Lean' if lean_results['mape'] < full_results['mape'] else 'Full'
    print(f"{'MAPE':<15} {lean_results['mape']:<12.2f} {full_results['mape']:<12.2f} {mape_winner:<10}")
    
    # Overall recommendation
    print(f"\n4. RECOMMENDATION")
    print("-" * 30)
    
    lean_wins = sum([
        lean_results['r2_score'] > full_results['r2_score'],
        lean_results['mae'] < full_results['mae'],
        lean_results['rmse'] < full_results['rmse'],
        lean_results['mape'] < full_results['mape']
    ])
    
    if lean_wins >= 3:
        recommendation = "LEAN"
        reason = f"Lean approach wins {lean_wins}/4 metrics with fewer features"
    elif lean_wins <= 1:
        recommendation = "FULL"
        reason = f"Full approach wins {4-lean_wins}/4 metrics despite more features"
    else:
        recommendation = "MIXED"
        reason = "Results are mixed - consider domain expertise"
    
    print(f"Recommended approach: {recommendation}")
    print(f"Reason: {reason}")
    
    # Feature importance analysis
    if recommendation == "LEAN":
        print(f"\n5. LEAN FEATURES ANALYSIS")
        print("-" * 30)
        print("Essential features that provide good performance:")
        for i, feature in enumerate(lean_results['features'], 1):
            print(f"  {i}. {feature}")
    
    return results

def show_feature_correlations():
    """Show correlation between available environmental features"""
    print(f"\n6. ENVIRONMENTAL FEATURE CORRELATIONS")
    print("-" * 30)
    
    # Generate sample data
    generator = WaterUsageDataGenerator(n_users=5, n_months=12)  # Smaller for analysis
    df = generator.generate_dataset()
    
    # Environmental features
    env_features = ['temperature_min', 'temperature_max', 'rainfall', 'humidity', 
                   'wind_speed', 'sunshine_duration', 'surface_pressure']
    
    # Calculate correlations
    corr_matrix = df[env_features].corr()
    
    print("Correlation matrix (values close to 1 or -1 indicate redundancy):")
    print(corr_matrix.round(3))
    
    # Find highly correlated pairs
    print(f"\nHighly correlated pairs (>0.7 or <-0.7):")
    for i in range(len(env_features)):
        for j in range(i+1, len(env_features)):
            corr_val = corr_matrix.iloc[i, j]
            if abs(corr_val) > 0.7:
                print(f"  {env_features[i]} ↔ {env_features[j]}: {corr_val:.3f}")
    
    print(f"\nRecommendation: Avoid using highly correlated features together")

if __name__ == "__main__":
    # Run comparison
    results = compare_feature_strategies()
    
    # Show correlations
    show_feature_correlations()
    
    print(f"\n" + "=" * 60)
    print("FEATURE COMPARISON COMPLETED!")
    print("=" * 60)
