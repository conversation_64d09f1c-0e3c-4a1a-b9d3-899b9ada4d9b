# Main Test Script for Water Usage Prediction Pipeline
import sys
from pathlib import Path
import pandas as pd
from datetime import datetime

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from data_generator import WaterUsageDataGenerator
from feature_engineering import WaterUsageFeatureEngineer
from model_trainer import WaterUsageModelTrainer
from predictor import WaterUsagePredictor
from config import *

def run_complete_pipeline():
    """Run the complete water usage prediction pipeline"""
    print("=" * 60)
    print("WATER USAGE PREDICTION PIPELINE TEST")
    print("=" * 60)

    # Step 1: Generate synthetic data
    print("\n1. GENERATING SYNTHETIC DATA")
    print("-" * 30)

    generator = WaterUsageDataGenerator(n_users=N_USERS, n_months=N_MONTHS)
    df_raw = generator.generate_dataset()

    # Save raw data
    raw_data_path = generator.save_dataset(df_raw, "synthetic_water_data.csv")

    print(f"✓ Generated {len(df_raw)} records for {df_raw['user_id'].nunique()} users")
    print(f"✓ Date range: {df_raw['date'].min()} to {df_raw['date'].max()}")
    print(f"✓ Average usage: {df_raw['usage'].mean():.2f} m³")

    # Step 2: Feature engineering
    print("\n2. FEATURE ENGINEERING")
    print("-" * 30)

    engineer = WaterUsageFeatureEngineer()
    df_features = engineer.engineer_features(df_raw)

    # Prepare model data
    X, y, feature_cols = engineer.prepare_model_data(df_features)

    print(f"✓ Created {len(feature_cols)} features")
    print(f"✓ Model data shape: X={X.shape}, y={y.shape}")

    # Save engineered data
    engineered_data_path = DATA_DIR / "engineered_water_data.csv"
    df_features.to_csv(engineered_data_path, index=False)
    print(f"✓ Saved engineered data to: {engineered_data_path}")

    # Step 3: Model training
    print("\n3. MODEL TRAINING")
    print("-" * 30)

    trainer = WaterUsageModelTrainer()

    # Create time series split
    X_train, X_test, y_train, y_test = trainer.create_time_series_split(X, y, test_size=0.2)

    # Train model
    trainer.train_model(X_train, y_train)

    # Evaluate model
    metrics = trainer.evaluate_model(X_test, y_test)

    print(f"✓ Model R² Score: {metrics['r2_score']:.3f}")
    print(f"✓ Model MAE: {metrics['mae']:.3f} m³")
    print(f"✓ Model RMSE: {metrics['rmse']:.3f} m³")

    # Show top features
    top_features = trainer.get_feature_importance(10)
    print(f"✓ Top 5 features: {', '.join(top_features['feature'].head().tolist())}")

    # Save model
    model_path = trainer.save_model()
    print(f"✓ Model saved to: {model_path}")

    # Step 4: Generate predictions
    print("\n4. GENERATING PREDICTIONS")
    print("-" * 30)

    predictor = WaterUsagePredictor(model_path)

    # Use recent data for prediction testing
    recent_data = df_features.tail(N_USERS * 3)  # Last 3 months for each user
    target_month = "2024-01"

    # Generate predictions
    predictions = predictor.predict_batch_users(recent_data, target_month)
    summary = predictor.generate_prediction_summary(predictions)

    print(f"✓ Generated predictions for {summary['successful_predictions']} users")
    print(f"✓ Average predicted usage: {summary['average_predicted_usage']} m³")
    print(f"✓ Average confidence: {summary['average_confidence']}")
    print(f"✓ Users with increase: {summary['users_with_increase']}")
    print(f"✓ Users with decrease: {summary['users_with_decrease']}")

    # Save predictions
    predictions_path = predictor.save_predictions(predictions, "test_predictions.csv")
    print(f"✓ Predictions saved to: {predictions_path}")

    # Step 5: Display sample results
    print("\n5. SAMPLE PREDICTION RESULTS")
    print("-" * 30)

    print("Format: user_id | prediction_month | predicted_usage | change_direction | change_percentage | confidence_score")
    print("-" * 100)

    for i, pred in enumerate(predictions[:10]):  # Show first 10
        if pred.get('predicted_usage') is not None:
            print(f"{pred['user_id']} | {pred['prediction_month']} | "
                  f"{pred['predicted_usage']:.2f} m³ | {pred['change_direction']} | "
                  f"{pred['change_percentage']:.1f}% | {pred['confidence_score']:.3f}")

    # Step 6: Pipeline summary
    print("\n6. PIPELINE SUMMARY")
    print("-" * 30)

    pipeline_summary = {
        "data_generation": "✓ Complete",
        "feature_engineering": "✓ Complete",
        "model_training": f"✓ Complete (R²={metrics['r2_score']:.3f})",
        "prediction_generation": f"✓ Complete ({summary['successful_predictions']} users)",
        "output_format": "✓ Matches requirements",
        "files_created": [
            str(raw_data_path),
            str(engineered_data_path),
            str(model_path),
            str(predictions_path)
        ]
    }

    for key, value in pipeline_summary.items():
        if key != "files_created":
            print(f"{key.replace('_', ' ').title()}: {value}")

    print(f"\nFiles created:")
    for file_path in pipeline_summary["files_created"]:
        print(f"  - {file_path}")

    print("\n" + "=" * 60)
    print("PIPELINE TEST COMPLETED SUCCESSFULLY!")
    print("=" * 60)

    return {
        "raw_data": df_raw,
        "engineered_data": df_features,
        "model": trainer,
        "predictions": predictions,
        "summary": summary,
        "metrics": metrics
    }

def test_prediction_format():
    """Test that predictions match the required format"""
    print("\nTESTING PREDICTION FORMAT")
    print("-" * 30)

    required_fields = [
        'user_id', 'prediction_month', 'predicted_usage',
        'change_direction', 'change_percentage', 'confidence_score'
    ]

    # Load test predictions
    predictions_file = DATA_DIR / "test_predictions.csv"
    if predictions_file.exists():
        df_pred = pd.read_csv(predictions_file)

        print("Required fields check:")
        for field in required_fields:
            exists = field in df_pred.columns
            print(f"  {field}: {'✓' if exists else '✗'}")

        # Check data types and ranges
        if 'predicted_usage' in df_pred.columns:
            usage_values = df_pred['predicted_usage'].dropna()
            print(f"\nPredicted usage range: {usage_values.min():.2f} - {usage_values.max():.2f} m³")

        if 'confidence_score' in df_pred.columns:
            conf_values = df_pred['confidence_score'].dropna()
            print(f"Confidence score range: {conf_values.min():.3f} - {conf_values.max():.3f}")

        if 'change_direction' in df_pred.columns:
            directions = df_pred['change_direction'].value_counts()
            print(f"Change directions: {directions.to_dict()}")

        print("✓ Format validation complete")
    else:
        print("✗ No predictions file found")

if __name__ == "__main__":
    # Run complete pipeline
    results = run_complete_pipeline()

    # Test prediction format
    test_prediction_format()

    print(f"\nTest completed at: {datetime.now()}")