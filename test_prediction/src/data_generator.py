# Synthetic Data Generator for Water Usage Prediction
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import *

class WaterUsageDataGenerator:
    """Generate realistic synthetic water usage data for Sarawak, Malaysia"""

    def __init__(self, n_users: int = N_USERS, n_months: int = N_MONTHS):
        self.n_users = n_users
        self.n_months = n_months
        self.start_date = datetime(2021, 1, 1)  # Start from Jan 2021

    def generate_time_features(self) -> pd.DataFrame:
        """Generate time-based features"""
        dates = []
        for i in range(self.n_months):
            date = self.start_date + timedelta(days=30*i)  # Approximate monthly
            dates.append(date)

        df = pd.DataFrame({'date': dates})
        df['month'] = df['date'].dt.month
        df['quarter'] = df['date'].dt.quarter
        df['year'] = df['date'].dt.year
        df['days_in_month'] = df['date'].dt.days_in_month

        # Note: We'll determine wet/dry periods dynamically based on actual rainfall
        # instead of hardcoded months

        return df

    def generate_environmental_data(self) -> pd.DataFrame:
        """Generate realistic environmental data for Sarawak"""
        time_df = self.generate_time_features()

        environmental_data = []

        for _, row in time_df.iterrows():
            month = row['month']
            # Use probabilistic approach for wet season (Nov-Mar more likely to be wet)
            is_wet_season_likely = month in [11, 12, 1, 2, 3]

            # Temperature (Sarawak is consistently warm)
            base_temp = SARAWAK_CLIMATE['temperature']['avg']
            temp_variation = 2 * np.sin(2 * np.pi * month / 12)  # Seasonal variation
            temp_avg = base_temp + temp_variation + np.random.normal(0, 1)
            temp_avg = np.clip(temp_avg,
                                SARAWAK_CLIMATE['temperature']['min'],
                                SARAWAK_CLIMATE['temperature']['max'])

            # Temperature min/max (daily range ~6-8°C)
            temp_range = np.random.uniform(6, 8)
            temperature_min = temp_avg - temp_range/2
            temperature_max = temp_avg + temp_range/2

            # Rainfall (higher probability during traditional wet season)
            if is_wet_season_likely:
                base_rainfall = SARAWAK_CLIMATE['rainfall']['wet_season']
                if month in [12, 1]:  # Peak wet season
                    base_rainfall = SARAWAK_CLIMATE['rainfall']['peak_wet']
            else:
                base_rainfall = SARAWAK_CLIMATE['rainfall']['dry_season']

            rainfall = max(0, np.random.gamma(2, base_rainfall/2))

            # Humidity (higher during wet periods)
            base_humidity = SARAWAK_CLIMATE['humidity']['avg']
            humidity_boost = 5 if is_wet_season_likely else -5
            humidity = base_humidity + humidity_boost + np.random.normal(0, 3)
            humidity = np.clip(humidity,
                             SARAWAK_CLIMATE['humidity']['min'],
                             SARAWAK_CLIMATE['humidity']['max'])

            # Wind speed (slightly higher during wet season)
            base_wind = SARAWAK_CLIMATE['wind_speed']['avg']
            wind_boost = 2 if is_wet_season_likely else -1
            wind_speed = base_wind + wind_boost + np.random.normal(0, 2)
            wind_speed = np.clip(wind_speed,
                               SARAWAK_CLIMATE['wind_speed']['min'],
                               SARAWAK_CLIMATE['wind_speed']['max'])

            # Sunshine duration (hours per day, less during wet season)
            base_sunshine = 8 if not is_wet_season_likely else 5  # Tropical average
            sunshine_duration = base_sunshine + np.random.normal(0, 1)
            sunshine_duration = max(2, min(12, sunshine_duration))  # 2-12 hours

            # Surface pressure (relatively stable in tropics)
            base_pressure = 1013  # hPa at sea level
            pressure_variation = np.random.normal(0, 3)  # Small variations
            surface_pressure = base_pressure + pressure_variation

            environmental_data.append({
                'date': row['date'],
                'month': month,
                'quarter': row['quarter'],
                'year': row['year'],
                'days_in_month': row['days_in_month'],
                'temperature_min': round(temperature_min, 1),
                'temperature_max': round(temperature_max, 1),
                'rainfall': round(rainfall, 1),
                'humidity': round(humidity, 1),
                'wind_speed': round(wind_speed, 1),
                'sunshine_duration': round(sunshine_duration, 1),
                'surface_pressure': round(surface_pressure, 1)
            })

        return pd.DataFrame(environmental_data)

    def generate_water_usage(self, env_df: pd.DataFrame) -> pd.DataFrame:
        """Generate realistic water usage patterns"""
        all_data = []

        for user_id in range(1, self.n_users + 1):
            # Each user has slightly different base consumption
            user_base_usage = np.random.normal(
                USAGE_PATTERNS['residential_avg'],
                USAGE_PATTERNS['residential_std'] / 3
            )
            user_base_usage = max(10, user_base_usage)  # Minimum 10 m³

            user_data = env_df.copy()
            user_data['user_id'] = f"USER_{user_id:03d}"

            usage_values = []

            for i, row in user_data.iterrows():
                # Base usage for this user
                base_usage = user_base_usage

                # Seasonal effect based on actual rainfall (higher usage during dry periods)
                seasonal_multiplier = 1.0
                if row['rainfall'] < RAINFALL_THRESHOLDS['dry']:  # Dry period
                    seasonal_multiplier = 1 + USAGE_PATTERNS['seasonal_factor']

                # Annual trend (slight increase over time)
                years_passed = (row['year'] - 2021)
                trend_multiplier = 1 + (USAGE_PATTERNS['trend_factor'] * years_passed)

                # Environmental effects
                temp_effect = (row['temperature_max'] - 26) * 0.5  # Higher temp = more usage
                rainfall_effect = -row['rainfall'] * 0.01  # More rain = less usage
                humidity_effect = (row['humidity'] - 85) * -0.05  # Higher humidity = less usage

                # Calculate usage
                usage = (base_usage * seasonal_multiplier * trend_multiplier +
                        temp_effect + rainfall_effect + humidity_effect)

                # Add some random noise
                usage += np.random.normal(0, USAGE_PATTERNS['residential_std'] * 0.3)

                # Ensure positive usage
                usage = max(5, usage)

                usage_values.append(round(usage, 2))

            user_data['usage'] = usage_values
            all_data.append(user_data)

        return pd.concat(all_data, ignore_index=True)

    def generate_dataset(self) -> pd.DataFrame:
        """Generate complete dataset"""
        print(f"Generating synthetic data for {self.n_users} users over {self.n_months} months...")

        # Generate environmental data
        env_df = self.generate_environmental_data()
        print(f"Generated environmental data: {len(env_df)} months")

        # Generate water usage data
        full_df = self.generate_water_usage(env_df)
        print(f"Generated usage data: {len(full_df)} records")

        # Sort by user and date
        full_df = full_df.sort_values(['user_id', 'date']).reset_index(drop=True)

        return full_df

    def save_dataset(self, df: pd.DataFrame, filename: str = "synthetic_water_data.csv"):
        """Save dataset to file"""
        filepath = DATA_DIR / filename
        df.to_csv(filepath, index=False)
        print(f"Dataset saved to: {filepath}")
        return filepath

def main():
    """Generate and save synthetic dataset"""
    generator = WaterUsageDataGenerator()
    df = generator.generate_dataset()

    # Display basic statistics
    print("\n=== Dataset Summary ===")
    print(f"Shape: {df.shape}")
    print(f"Users: {df['user_id'].nunique()}")
    print(f"Date range: {df['date'].min()} to {df['date'].max()}")
    print(f"Average usage: {df['usage'].mean():.2f} m³")
    print(f"Usage std: {df['usage'].std():.2f} m³")

    # Save dataset
    filepath = generator.save_dataset(df)

    return df, filepath

if __name__ == "__main__":
    df, filepath = main()
