# Water Usage Prediction System - Core Package
"""
Core package for water usage prediction system.

This package contains the main modules for:
- Data generation and processing
- Feature engineering
- Model training and prediction
- Utilities and helpers
"""

__version__ = "1.0.0"
__author__ = "Water Usage Prediction Team"

# Import main classes for easy access
try:
    from .data_generator import DataGenerator
    from .feature_engineering import FeatureEngineer
    from .model_trainer import ModelTrainer
    from .predictor import Predictor
except ImportError:
    # Handle case where modules might not be fully implemented yet
    pass

__all__ = [
    'DataGenerator',
    'FeatureEngineer', 
    'ModelTrainer',
    'Predictor'
]
